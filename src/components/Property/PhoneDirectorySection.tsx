import React from "react";
import { StyleSheet, Text, TouchableOpacity, View, Linking, Alert } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../../theme";
import { PhoneDirectory } from "../../interfaces/phone-directory";
import { Card } from "../main/Card";
import { Row } from "../main/Row";
import { Col } from "../main/Col";
import { Section } from "../main/Section";
import { SmartAvatar } from "../main/Avatar/SmartAvatar";

interface PhoneDirectorySectionProps {
  phoneDirectory: PhoneDirectory[];
  onViewAll?: () => void;
}

const handleCall = (phoneNumber: string, name: string) => {
  const url = `tel:${phoneNumber}`;
  Linking.canOpenURL(url)
    .then((supported) => {
      if (!supported) {
        Alert.alert("Error", "No se puede hacer la llamada");
      } else {
        Alert.alert(
          "<PERSON>lam<PERSON>",
          `¿Deseas llamar a ${name}?`,
          [
            { text: "Cancelar", style: "cancel" },
            { text: "Llamar", onPress: () => Linking.openURL(url) }
          ]
        );
      }
    })
    .catch((err) => console.error("Error al intentar llamar:", err));
};

const getContactTypeIcon = (name: string): string => {
  const nameLower = name.toLowerCase();
  if (nameLower.includes("emergencia") || nameLower.includes("911") || nameLower.includes("bomberos")) {
    return "fire-truck";
  } else if (nameLower.includes("policia") || nameLower.includes("policía") || nameLower.includes("seguridad")) {
    return "shield-account";
  } else if (nameLower.includes("medico") || nameLower.includes("médico") || nameLower.includes("hospital") || nameLower.includes("ambulancia")) {
    return "medical-bag";
  } else if (nameLower.includes("administra") || nameLower.includes("oficina") || nameLower.includes("recepcion")) {
    return "office-building";
  } else if (nameLower.includes("manteni") || nameLower.includes("conserje") || nameLower.includes("limpieza")) {
    return "wrench";
  } else if (nameLower.includes("portero") || nameLower.includes("vigilancia") || nameLower.includes("caseta")) {
    return "security";
  } else {
    return "phone";
  }
};

const getContactTypeColor = (name: string): string => {
  const nameLower = name.toLowerCase();
  if (nameLower.includes("emergencia") || nameLower.includes("911") || nameLower.includes("bomberos")) {
    return theme.colors.error;
  } else if (nameLower.includes("policia") || nameLower.includes("policía") || nameLower.includes("seguridad")) {
    return theme.colors.warning;
  } else if (nameLower.includes("medico") || nameLower.includes("médico") || nameLower.includes("hospital")) {
    return theme.colors.success;
  } else {
    return theme.colors.primary;
  }
};

const isEmergencyContact = (name: string): boolean => {
  const nameLower = name.toLowerCase();
  return nameLower.includes("emergencia") || 
         nameLower.includes("911") || 
         nameLower.includes("bomberos") || 
         nameLower.includes("policia") || 
         nameLower.includes("policía") ||
         nameLower.includes("ambulancia");
};

export const PhoneDirectorySection: React.FC<PhoneDirectorySectionProps> = ({
  phoneDirectory,
  onViewAll,
}) => {
  if (!phoneDirectory.length) {
    return (
      <Section title="Directorio Telefónico">
        <Text style={styles.noDataText}>No hay contactos disponibles</Text>
      </Section>
    );
  }

  // Separar contactos de emergencia de los regulares
  const emergencyContacts = phoneDirectory.filter(contact => isEmergencyContact(contact.name));
  const regularContacts = phoneDirectory.filter(contact => !isEmergencyContact(contact.name));

  return (
    <Section title="Directorio Telefónico">
      <Col>
        {/* Contactos de emergencia */}
        {emergencyContacts.length > 0 && (
          <View style={styles.emergencySection}>
            <Text style={styles.emergencyTitle}>🚨 Contactos de Emergencia</Text>
            {emergencyContacts.map((contact) => (
              <TouchableOpacity
                key={contact.id}
                onPress={() => handleCall(contact.phoneNumber, contact.name)}
                activeOpacity={0.7}
              >
                <Card style={styles.emergencyCard}>
                  <Row align="center">
                    <View style={[
                      styles.contactIndicator,
                      { backgroundColor: `${getContactTypeColor(contact.name)}20` }
                    ]}>
                      <MaterialCommunityIcons
                        name={getContactTypeIcon(contact.name)}
                        size={24}
                        color={getContactTypeColor(contact.name)}
                      />
                    </View>
                    <Col style={styles.contactInfo}>
                      <Text style={styles.emergencyContactName}>
                        {contact.name}
                      </Text>
                      <Text style={styles.emergencyContactPhone}>
                        {contact.phoneNumber}
                      </Text>
                    </Col>
                    <MaterialCommunityIcons
                      name="phone"
                      size={24}
                      color={theme.colors.error}
                    />
                  </Row>
                </Card>
              </TouchableOpacity>
            ))}
          </View>
        )}

        {/* Resumen y acceso rápido */}
        <TouchableOpacity onPress={onViewAll} activeOpacity={0.7}>
          <Card style={styles.summaryCard}>
            <Row align="center" style={styles.summaryHeader}>
              <View style={styles.summaryIcon}>
                <MaterialCommunityIcons
                  name="contacts"
                  size={32}
                  color={theme.colors.primary}
                />
              </View>
              <Col style={styles.summaryInfo}>
                <Text style={styles.summaryTitle}>Directorio Completo</Text>
                <Text style={styles.summarySubtitle}>
                  {phoneDirectory.length} contactos disponibles
                </Text>
              </Col>
              <MaterialCommunityIcons
                name="chevron-right"
                size={24}
                color={theme.colors.primary}
              />
            </Row>
          </Card>
        </TouchableOpacity>

        {/* Contactos regulares más importantes */}
        {regularContacts.length > 0 && (
          <View style={styles.regularSection}>
            <Text style={styles.regularTitle}>Contactos Principales</Text>
            {regularContacts.slice(0, 3).map((contact) => (
              <TouchableOpacity
                key={contact.id}
                onPress={() => handleCall(contact.phoneNumber, contact.name)}
                activeOpacity={0.7}
              >
                <Card style={styles.contactCard}>
                  <Row align="center">
                    <SmartAvatar name={contact.name} size={40} />
                    <Col style={styles.contactInfo}>
                      <Text style={styles.contactName} numberOfLines={1}>
                        {contact.name}
                      </Text>
                      <Text style={styles.contactPhone}>
                        {contact.phoneNumber}
                      </Text>
                    </Col>
                    <TouchableOpacity
                      style={styles.callButton}
                      onPress={() => handleCall(contact.phoneNumber, contact.name)}
                      activeOpacity={0.7}
                    >
                      <MaterialCommunityIcons
                        name="phone"
                        size={20}
                        color={theme.colors.white}
                      />
                    </TouchableOpacity>
                  </Row>
                </Card>
              </TouchableOpacity>
            ))}
            
            {regularContacts.length > 3 && (
              <Text style={styles.moreContactsText}>
                Y {regularContacts.length - 3} contactos más...
              </Text>
            )}
          </View>
        )}
      </Col>
    </Section>
  );
};

const styles = StyleSheet.create({
  emergencySection: {
    marginBottom: theme.spacing.lg,
  },
  emergencyTitle: {
    fontSize: theme.fontSizes.md,
    fontWeight: "700",
    color: theme.colors.error,
    marginBottom: theme.spacing.sm,
    textAlign: "center",
  },
  emergencyCard: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
    borderLeftWidth: 4,
    borderLeftColor: theme.colors.error,
    backgroundColor: `${theme.colors.error}05`,
  },
  contactIndicator: {
    width: 48,
    height: 48,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  contactInfo: {
    flex: 1,
  },
  emergencyContactName: {
    fontSize: theme.fontSizes.md,
    fontWeight: "700",
    color: theme.colors.error,
    marginBottom: 2,
  },
  emergencyContactPhone: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "600",
    color: theme.colors.black,
  },
  summaryCard: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: theme.spacing.lg,
    paddingVertical: theme.spacing.lg,
    paddingHorizontal: theme.spacing.md,
  },
  summaryHeader: {
    width: "100%",
  },
  summaryIcon: {
    width: 60,
    height: 60,
    borderRadius: theme.radii.lg,
    backgroundColor: `${theme.colors.primary}20`,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  summaryInfo: {
    flex: 1,
  },
  summaryTitle: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.black,
    marginBottom: 2,
  },
  summarySubtitle: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
  },
  regularSection: {
    marginTop: theme.spacing.sm,
  },
  regularTitle: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    marginBottom: theme.spacing.sm,
  },
  contactCard: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  contactName: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    marginBottom: 2,
  },
  contactPhone: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
  },
  callButton: {
    backgroundColor: theme.colors.primary,
    width: 40,
    height: 40,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
  },
  moreContactsText: {
    textAlign: "center",
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    fontStyle: "italic",
    marginTop: theme.spacing.sm,
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
