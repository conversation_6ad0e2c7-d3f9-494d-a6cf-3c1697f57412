import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../../theme";
import { PartialProperty } from "../../interfaces/me";
import { PropertyType } from "../../interfaces/property";

interface PropertySelectorProps {
  properties: PartialProperty[];
  selectedPropertyId: string | null;
  onPropertySelect: (propertyId: string) => void;
}

const getPropertyTypeLabel = (type: PropertyType): string => {
  switch (type) {
    case PropertyType.HOUSE:
      return "Casa";
    case PropertyType.DEPARTMENT:
      return "Departamento";
    default:
      return "Propiedad";
  }
};

const getPropertyIcon = (type: PropertyType): string => {
  switch (type) {
    case PropertyType.HOUSE:
      return "home";
    case PropertyType.DEPARTMENT:
      return "office-building";
    default:
      return "map-marker";
  }
};

export const PropertySelector: React.FC<PropertySelectorProps> = ({
  properties,
  selectedPropertyId,
  onPropertySelect,
}) => {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Seleccionar propiedad</Text>
      <View style={styles.propertiesContainer}>
        {properties.map((property) => {
          const isSelected = property.id === selectedPropertyId;
          return (
            <TouchableOpacity
              key={property.id}
              style={[
                styles.propertyCard,
                isSelected && styles.selectedPropertyCard,
              ]}
              onPress={() => onPropertySelect(property.id)}
            >
              <View style={styles.propertyHeader}>
                <MaterialCommunityIcons
                  name={getPropertyIcon(property.type)}
                  size={theme.fontSizes.lg}
                  color={isSelected ? theme.colors.white : theme.colors.primary}
                />
                <Text
                  style={[
                    styles.propertyType,
                    isSelected && styles.selectedText,
                  ]}
                >
                  {getPropertyTypeLabel(property.type)}
                </Text>
              </View>
              <Text
                style={[
                  styles.propertyAddress,
                  isSelected && styles.selectedText,
                ]}
                numberOfLines={2}
                ellipsizeMode="tail"
              >
                {property.address}
              </Text>
              {isSelected && (
                <View style={styles.selectedIndicator}>
                  <MaterialCommunityIcons
                    name="check-circle"
                    size={theme.fontSizes.md}
                    color={theme.colors.white}
                  />
                </View>
              )}
            </TouchableOpacity>
          );
        })}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: theme.spacing.lg,
  },
  title: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "600",
    color: theme.colors.primary,
    marginBottom: theme.spacing.md,
    textAlign: "center",
  },
  propertiesContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  propertyCard: {
    backgroundColor: theme.colors.white,
    borderRadius: theme.radii.lg,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.sm,
    width: "48%",
    minHeight: 100,
    borderWidth: 2,
    borderColor: theme.colors.primaryLight,
    position: "relative",
  },
  selectedPropertyCard: {
    backgroundColor: theme.colors.primary,
    borderColor: theme.colors.primaryDark,
  },
  propertyHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: theme.spacing.sm,
  },
  propertyType: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.primary,
    marginLeft: theme.spacing.sm,
  },
  propertyAddress: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray700,
    lineHeight: 18,
  },
  selectedText: {
    color: theme.colors.white,
  },
  selectedIndicator: {
    position: "absolute",
    top: theme.spacing.xs,
    right: theme.spacing.xs,
  },
});
