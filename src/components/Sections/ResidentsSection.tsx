import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Section } from "../main/Section";
import { Col, Row } from "../main";
import { Card } from "../main/Card";
import { SmartAvatar } from "../main/Avatar/SmartAvatar";
import { theme } from "../../theme";
import { PartialResident } from "../../interfaces/me";

interface ResidentsSectionProps {
  residents: PartialResident[];
}

const getRoleIcon = (roleName: string): string => {
  const role = roleName.toLowerCase();
  if (role.includes("propietario") || role.includes("owner")) {
    return "crown";
  } else if (role.includes("inquilino") || role.includes("tenant")) {
    return "home-account";
  } else if (role.includes("administrador") || role.includes("admin")) {
    return "shield-account";
  } else if (role.includes("conserje") || role.includes("portero")) {
    return "account-tie";
  } else {
    return "account";
  }
};

const getRoleColor = (roleName: string): string => {
  const role = roleName.toLowerCase();
  if (role.includes("propietario") || role.includes("owner")) {
    return theme.colors.gold;
  } else if (role.includes("administrador") || role.includes("admin")) {
    return theme.colors.primary;
  } else if (role.includes("conserje") || role.includes("portero")) {
    return theme.colors.secondary;
  } else {
    return theme.colors.gray500;
  }
};

export const ResidentsSection: React.FC<ResidentsSectionProps> = ({
  residents,
}) => {
  if (!residents.length) {
    return (
      <Section title="Residentes">
        <Text style={styles.noDataText}>No hay residentes registrados</Text>
      </Section>
    );
  }

  return (
    <Section title="Residentes">
      <Col>
        {residents.map((resident) => {
          const name = `${resident.firstName} ${resident.paternalLastName}`;
          const fullName = `${name} ${resident.maternalLastName}`;

          return (
            <TouchableOpacity key={resident.id} activeOpacity={0.7}>
              <Card style={styles.residentCard}>
                <Row align="center">
                  <SmartAvatar name={name} size={50} />
                  <Col style={styles.residentInfo}>
                    <Text style={styles.residentName} numberOfLines={1}>
                      {fullName}
                    </Text>
                    <Text style={styles.residentEmail} numberOfLines={1}>
                      {resident.email}
                    </Text>
                    <Row style={styles.rolesContainer}>
                      {resident.roles.map((role) => (
                        <View
                          key={role.id}
                          style={[
                            styles.roleChip,
                            { backgroundColor: `${getRoleColor(role.name)}20` },
                          ]}
                        >
                          <MaterialCommunityIcons
                            name={getRoleIcon(role.name)}
                            size={12}
                            color={getRoleColor(role.name)}
                          />
                          <Text
                            style={[
                              styles.roleText,
                              { color: getRoleColor(role.name) },
                            ]}
                          >
                            {role.name}
                          </Text>
                        </View>
                      ))}
                    </Row>
                  </Col>
                  <MaterialCommunityIcons
                    name="chevron-right"
                    size={theme.fontSizes.lg}
                    color={theme.colors.gray400}
                  />
                </Row>
              </Card>
            </TouchableOpacity>
          );
        })}
      </Col>
    </Section>
  );
};

const styles = StyleSheet.create({
  residentCard: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  residentInfo: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  residentName: {
    fontSize: theme.fontSizes.md,
    fontWeight: "600",
    color: theme.colors.black,
    marginBottom: 2,
  },
  residentEmail: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    marginBottom: theme.spacing.xs,
  },
  rolesContainer: {
    flexWrap: "wrap",
    gap: theme.spacing.xs,
  },
  roleChip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.md,
    marginRight: theme.spacing.xs,
    marginBottom: 2,
  },
  roleText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    marginLeft: 4,
    textTransform: "capitalize",
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
