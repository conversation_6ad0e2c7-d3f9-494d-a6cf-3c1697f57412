import { StyleSheet, Text, TouchableOpacity } from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Card, Section, Row } from "../main";
import { PartialMaintenanceIssueReport } from "../../interfaces/me";
import { Status } from "../../interfaces/maintenance-issue-report";
import { theme } from "../../theme";

interface MaintenanceIssueReportsSectionProps {
  maintenanceIssueReports: PartialMaintenanceIssueReport[];
  onViewAll?: () => void;
  onViewByStatus?: (status: Status) => void;
}

export const MaintenanceIssueReportsSection: React.FC<
  MaintenanceIssueReportsSectionProps
> = ({ maintenanceIssueReports, onViewAll, onViewByStatus }) => {
  if (!maintenanceIssueReports.length) {
    return (
      <Section title="Reportes de mantenimiento">
        <Text style={styles.noDataText}>No hay reportes de mantenimiento</Text>
      </Section>
    );
  }

  // Separar por estado
  const openReports = maintenanceIssueReports.filter(
    (r) =>
      r.status.toLowerCase().includes("abierto") ||
      r.status.toLowerCase().includes("open")
  );
  const inProgressReports = maintenanceIssueReports.filter(
    (r) =>
      r.status.toLowerCase().includes("progreso") ||
      r.status.toLowerCase().includes("progress")
  );
  const resolvedReports = maintenanceIssueReports.filter(
    (r) =>
      r.status.toLowerCase().includes("resuelto") ||
      r.status.toLowerCase().includes("resolved") ||
      r.status.toLowerCase().includes("completado")
  );

  return (
    <Section title="Reportes de mantenimiento">
      <TouchableOpacity onPress={onViewAll} activeOpacity={0.7}>
        <Card style={styles.summaryCard}>
          <Row align="center" style={styles.summaryRow}>
            <TouchableOpacity
              style={styles.summaryItem}
              onPress={() => onViewByStatus?.(Status.OPEN)}
              activeOpacity={0.7}
            >
              <MaterialCommunityIcons
                name="clock-outline"
                size={24}
                color={theme.colors.primary}
              />
              <Text style={styles.summaryNumber}>{openReports.length}</Text>
              <Text style={styles.summaryLabel}>Abiertos</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.summaryItem}
              onPress={() => onViewByStatus?.(Status.IN_PROGRESS)}
              activeOpacity={0.7}
            >
              <MaterialCommunityIcons
                name="clock-fast"
                size={24}
                color={theme.colors.warning}
              />
              <Text style={styles.summaryNumber}>
                {inProgressReports.length}
              </Text>
              <Text style={styles.summaryLabel}>En Progreso</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.summaryItem}
              onPress={() => onViewByStatus?.(Status.RESOLVED)}
              activeOpacity={0.7}
            >
              <MaterialCommunityIcons
                name="check-circle"
                size={24}
                color={theme.colors.success}
              />
              <Text style={styles.summaryNumber}>{resolvedReports.length}</Text>
              <Text style={styles.summaryLabel}>Resueltos</Text>
            </TouchableOpacity>
          </Row>

          <Row align="center" style={styles.viewAllRow}>
            <Text style={styles.viewAllText}>Ver todos los reportes</Text>
            <MaterialCommunityIcons
              name="chevron-right"
              size={16}
              color={theme.colors.primary}
            />
          </Row>
        </Card>
      </TouchableOpacity>
    </Section>
  );
};

const styles = StyleSheet.create({
  summaryCard: {
    flexDirection: "column",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  summaryRow: {
    justifyContent: "space-around",
    width: "100%",
    marginBottom: theme.spacing.md,
  },
  summaryItem: {
    alignItems: "center",
    flex: 1,
    paddingVertical: theme.spacing.sm,
  },
  summaryNumber: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
  viewAllRow: {
    justifyContent: "center",
    paddingTop: theme.spacing.sm,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray200,
  },
  viewAllText: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.primary,
    fontWeight: "600",
    marginRight: theme.spacing.xs,
  },

  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
