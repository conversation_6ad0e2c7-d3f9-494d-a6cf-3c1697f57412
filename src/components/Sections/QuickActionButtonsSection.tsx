import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Section } from "../main";
import { theme } from "../../theme";
import { DASHBOARD_SCREENS } from "../../navigation/constants";

interface QuickActionButton {
  id: string;
  title: string;
  subtitle: string;
  icon: keyof typeof MaterialCommunityIcons.glyphMap;
  gradient: string[];
  screen: string;
}

const quickActionButtons: QuickActionButton[] = [
  {
    id: "complaint",
    title: "Nueva Queja",
    subtitle: "Reportar problema",
    icon: "flag-outline",
    gradient: [theme.colors.error, "#FF6B6B"],
    screen: DASHBOARD_SCREENS.CREATE_COMPLAINT,
  },
  {
    id: "maintenance",
    title: "Mantenimiento",
    subtitle: "Reportar daño",
    icon: "tools",
    gradient: [theme.colors.warning, "#FFB347"],
    screen: DASHBOARD_SCREENS.CREATE_MAINTENANCE_REPORT,
  },
];

export const QuickActionButtonsSection: React.FC = () => {
  const navigation = useNavigation();

  const handleActionPress = (screen: string) => {
    navigation.navigate(screen as any);
  };

  return (
    <Section title="Reportes">
      <View style={styles.container}>
        {quickActionButtons.map((action, index) => (
          <TouchableOpacity
            key={action.id}
            style={[
              styles.actionButton,
              { backgroundColor: action.gradient[0] },
              index === 0 ? styles.firstButton : styles.secondButton,
            ]}
            onPress={() => handleActionPress(action.screen)}
            activeOpacity={0.8}
          >
            <View style={styles.buttonContent}>
              <View style={styles.iconContainer}>
                <MaterialCommunityIcons
                  name={action.icon}
                  size={theme.fontSizes.xxl}
                  color={theme.colors.white}
                />
              </View>
              <View style={styles.textContainer}>
                <Text style={styles.buttonTitle}>{action.title}</Text>
                <Text style={styles.buttonSubtitle}>{action.subtitle}</Text>
              </View>
              <MaterialCommunityIcons
                name="arrow-right"
                size={theme.fontSizes.lg}
                color={theme.colors.white}
                style={styles.arrowIcon}
              />
            </View>
          </TouchableOpacity>
        ))}
      </View>
    </Section>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    gap: theme.spacing.sm,
  },
  actionButton: {
    flex: 1,
    borderRadius: theme.radii.xl,
    padding: theme.spacing.lg,
    minHeight: 100,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  firstButton: {
    // Estilo específico para el primer botón si es necesario
  },
  secondButton: {
    // Estilo específico para el segundo botón si es necesario
  },
  buttonContent: {
    flex: 1,
    justifyContent: "space-between",
  },
  iconContainer: {
    alignSelf: "flex-start",
    marginBottom: theme.spacing.sm,
  },
  textContainer: {
    flex: 1,
    justifyContent: "center",
  },
  buttonTitle: {
    fontSize: theme.fontSizes.md,
    fontWeight: "800",
    color: theme.colors.white,
    marginBottom: theme.spacing.xs,
  },
  buttonSubtitle: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.white,
    opacity: 0.9,
  },
  arrowIcon: {
    alignSelf: "flex-end",
    opacity: 0.8,
  },
});
