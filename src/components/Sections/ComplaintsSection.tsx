import { StyleSheet, Text, TouchableOpacity } from "react-native";
import { Complaint } from "../../interfaces/complaint";
import { Status } from "../../interfaces/maintenance-issue-report";
import { Card, Row, Section } from "../main";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../../theme";

interface ComplaintsSectionProps {
  complaints: Complaint[];
  onViewAll?: () => void;
  onViewByStatus?: (status: Status) => void;
}

export const ComplaintsSection: React.FC<ComplaintsSectionProps> = ({
  complaints,
  onViewAll,
  onViewByStatus,
}) => {
  if (!complaints.length) {
    return (
      <Section title="Quejas">
        <Text style={styles.noDataText}>No hay quejas registradas</Text>
      </Section>
    );
  }

  // Separar por estado
  const openComplaints = complaints.filter((c) => c.status === Status.OPEN);
  const inProgressComplaints = complaints.filter(
    (c) => c.status === Status.IN_PROGRESS
  );
  const resolvedComplaints = complaints.filter(
    (c) => c.status === Status.RESOLVED
  );

  return (
    <Section title="Quejas">
      <TouchableOpacity onPress={onViewAll} activeOpacity={0.7}>
        <Card style={styles.summaryCard}>
          <Row align="center" style={styles.summaryRow}>
            <TouchableOpacity
              style={styles.summaryItem}
              onPress={() => onViewByStatus?.(Status.OPEN)}
              activeOpacity={0.7}
            >
              <MaterialCommunityIcons
                name="clock-outline"
                size={24}
                color={theme.colors.primary}
              />
              <Text style={styles.summaryNumber}>{openComplaints.length}</Text>
              <Text style={styles.summaryLabel}>Abiertas</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.summaryItem}
              onPress={() => onViewByStatus?.(Status.IN_PROGRESS)}
              activeOpacity={0.7}
            >
              <MaterialCommunityIcons
                name="clock-fast"
                size={24}
                color={theme.colors.warning}
              />
              <Text style={styles.summaryNumber}>
                {inProgressComplaints.length}
              </Text>
              <Text style={styles.summaryLabel}>En Progreso</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.summaryItem}
              onPress={() => onViewByStatus?.(Status.RESOLVED)}
              activeOpacity={0.7}
            >
              <MaterialCommunityIcons
                name="check-circle"
                size={24}
                color={theme.colors.success}
              />
              <Text style={styles.summaryNumber}>
                {resolvedComplaints.length}
              </Text>
              <Text style={styles.summaryLabel}>Resueltas</Text>
            </TouchableOpacity>
          </Row>

          <Row align="center" style={styles.viewAllRow}>
            <Text style={styles.viewAllText}>Ver todas las quejas</Text>
            <MaterialCommunityIcons
              name="chevron-right"
              size={16}
              color={theme.colors.primary}
            />
          </Row>
        </Card>
      </TouchableOpacity>
    </Section>
  );
};

const styles = StyleSheet.create({
  summaryCard: {
    flexDirection: "column",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.lg,
  },
  summaryRow: {
    justifyContent: "space-around",
    width: "100%",
    marginBottom: theme.spacing.md,
  },
  summaryItem: {
    alignItems: "center",
    flex: 1,
    paddingVertical: theme.spacing.sm,
  },
  summaryNumber: {
    fontSize: theme.fontSizes.xl,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
  viewAllRow: {
    justifyContent: "center",
    paddingTop: theme.spacing.sm,
    borderTopWidth: 1,
    borderTopColor: theme.colors.gray200,
  },
  viewAllText: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.primary,
    fontWeight: "600",
    marginRight: theme.spacing.xs,
  },

  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
