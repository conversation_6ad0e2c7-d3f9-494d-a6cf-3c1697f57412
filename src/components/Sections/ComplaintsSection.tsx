import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { Complaint, Priority } from "../../interfaces/complaint";
import { Status } from "../../interfaces/maintenance-issue-report";
import { Card, Row, Section, Col } from "../main";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { theme } from "../../theme";
import { formatDateDMY } from "../../utils/date-time.utils";

interface ComplaintsSectionProps {
  complaints: Complaint[];
}

const getPriorityColor = (priority: Priority): string => {
  switch (priority) {
    case Priority.HIGH:
      return theme.colors.error;
    case Priority.MEDIUM:
      return theme.colors.warning;
    case Priority.LOW:
      return theme.colors.success;
    default:
      return theme.colors.gray500;
  }
};

const getPriorityIcon = (priority: Priority): string => {
  switch (priority) {
    case Priority.HIGH:
      return "alert-circle";
    case Priority.MEDIUM:
      return "alert";
    case Priority.LOW:
      return "information-outline";
    default:
      return "help-circle";
  }
};

const getPriorityLabel = (priority: Priority): string => {
  switch (priority) {
    case Priority.HIGH:
      return "Alta";
    case Priority.MEDIUM:
      return "Media";
    case Priority.LOW:
      return "Baja";
    default:
      return "Sin prioridad";
  }
};

const getStatusColor = (status: Status): string => {
  switch (status) {
    case Status.OPEN:
      return theme.colors.primary;
    case Status.IN_PROGRESS:
      return theme.colors.warning;
    case Status.RESOLVED:
      return theme.colors.success;
    default:
      return theme.colors.gray500;
  }
};

const getStatusIcon = (status: Status): string => {
  switch (status) {
    case Status.OPEN:
      return "clock-outline";
    case Status.IN_PROGRESS:
      return "clock-fast";
    case Status.RESOLVED:
      return "check-circle";
    default:
      return "help-circle";
  }
};

const getStatusLabel = (status: Status): string => {
  switch (status) {
    case Status.OPEN:
      return "Abierta";
    case Status.IN_PROGRESS:
      return "En Progreso";
    case Status.RESOLVED:
      return "Resuelta";
    default:
      return "Sin estado";
  }
};

export const ComplaintsSection: React.FC<ComplaintsSectionProps> = ({
  complaints,
}) => {
  if (!complaints.length) {
    return (
      <Section title="Quejas">
        <Text style={styles.noDataText}>No hay quejas registradas</Text>
      </Section>
    );
  }

  // Separar por estado
  const openComplaints = complaints.filter((c) => c.status === Status.OPEN);
  const inProgressComplaints = complaints.filter(
    (c) => c.status === Status.IN_PROGRESS
  );
  const resolvedComplaints = complaints.filter(
    (c) => c.status === Status.RESOLVED
  );

  return (
    <Section title="Quejas">
      <Col>
        {/* Resumen de estados */}
        <Card style={styles.summaryCard}>
          <Row align="center" style={styles.summaryRow}>
            <View style={styles.summaryItem}>
              <MaterialCommunityIcons
                name="clock-outline"
                size={20}
                color={theme.colors.primary}
              />
              <Text style={styles.summaryNumber}>{openComplaints.length}</Text>
              <Text style={styles.summaryLabel}>Abiertas</Text>
            </View>
            <View style={styles.summaryItem}>
              <MaterialCommunityIcons
                name="clock-fast"
                size={20}
                color={theme.colors.warning}
              />
              <Text style={styles.summaryNumber}>
                {inProgressComplaints.length}
              </Text>
              <Text style={styles.summaryLabel}>En Progreso</Text>
            </View>
            <View style={styles.summaryItem}>
              <MaterialCommunityIcons
                name="check-circle"
                size={20}
                color={theme.colors.success}
              />
              <Text style={styles.summaryNumber}>
                {resolvedComplaints.length}
              </Text>
              <Text style={styles.summaryLabel}>Resueltas</Text>
            </View>
          </Row>
        </Card>

        {/* Lista de quejas */}
        {complaints.slice(0, 5).map((complaint) => (
          <TouchableOpacity
            key={complaint.id}
            onPress={() => console.log("Pressed complaint:", complaint.id)}
            activeOpacity={0.7}
          >
            <Card style={styles.complaintCard}>
              <Row align="flex-start">
                <View
                  style={[
                    styles.priorityIndicator,
                    {
                      backgroundColor: `${getPriorityColor(
                        complaint.priority
                      )}20`,
                    },
                  ]}
                >
                  <MaterialCommunityIcons
                    name={getPriorityIcon(complaint.priority)}
                    size={24}
                    color={getPriorityColor(complaint.priority)}
                  />
                </View>
                <Col style={styles.complaintInfo}>
                  <Row align="center" style={styles.headerRow}>
                    <View
                      style={[
                        styles.priorityChip,
                        {
                          backgroundColor: getPriorityColor(complaint.priority),
                        },
                      ]}
                    >
                      <Text style={styles.priorityText}>
                        {getPriorityLabel(complaint.priority)}
                      </Text>
                    </View>
                    <View
                      style={[
                        styles.statusChip,
                        { backgroundColor: getStatusColor(complaint.status) },
                      ]}
                    >
                      <MaterialCommunityIcons
                        name={getStatusIcon(complaint.status)}
                        size={12}
                        color={theme.colors.white}
                      />
                      <Text style={styles.statusText}>
                        {getStatusLabel(complaint.status)}
                      </Text>
                    </View>
                  </Row>
                  <Text style={styles.complaintDetail} numberOfLines={2}>
                    {complaint.detail}
                  </Text>
                  <Row align="center" style={styles.dateRow}>
                    <MaterialCommunityIcons
                      name="calendar"
                      size={14}
                      color={theme.colors.gray500}
                    />
                    <Text style={styles.dateText}>
                      Creada: {formatDateDMY(complaint.createdAt)}
                    </Text>
                    {!!complaint.completedAt && (
                      <>
                        <MaterialCommunityIcons
                          name="check"
                          size={14}
                          color={theme.colors.success}
                          style={styles.completedIcon}
                        />
                        <Text style={styles.completedText}>
                          Completada: {formatDateDMY(complaint.completedAt)}
                        </Text>
                      </>
                    )}
                  </Row>
                </Col>
                <MaterialCommunityIcons
                  name="chevron-right"
                  size={theme.fontSizes.lg}
                  color={theme.colors.gray500}
                />
              </Row>
            </Card>
          </TouchableOpacity>
        ))}

        {complaints.length > 5 && (
          <Text style={styles.moreItemsText}>
            Y {complaints.length - 5} quejas más...
          </Text>
        )}
      </Col>
    </Section>
  );
};

const styles = StyleSheet.create({
  summaryCard: {
    flexDirection: "row",
    marginBottom: theme.spacing.md,
    paddingVertical: theme.spacing.md,
  },
  summaryRow: {
    justifyContent: "space-around",
    width: "100%",
  },
  summaryItem: {
    alignItems: "center",
    flex: 1,
  },
  summaryNumber: {
    fontSize: theme.fontSizes.lg,
    fontWeight: "700",
    color: theme.colors.black,
    marginTop: theme.spacing.xs,
  },
  summaryLabel: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    textAlign: "center",
    marginTop: 2,
  },
  complaintCard: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: theme.spacing.sm,
    paddingVertical: theme.spacing.md,
    paddingHorizontal: theme.spacing.md,
  },
  priorityIndicator: {
    width: 48,
    height: 48,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
    marginRight: theme.spacing.md,
  },
  complaintInfo: {
    flex: 1,
  },
  headerRow: {
    justifyContent: "space-between",
    marginBottom: theme.spacing.sm,
  },
  priorityChip: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  priorityText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    textTransform: "uppercase",
  },
  statusChip: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.radii.sm,
  },
  statusText: {
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    color: theme.colors.white,
    marginLeft: 4,
    textTransform: "uppercase",
  },
  complaintDetail: {
    fontSize: theme.fontSizes.md,
    color: theme.colors.black,
    lineHeight: 20,
    marginBottom: theme.spacing.sm,
  },
  dateRow: {
    marginTop: theme.spacing.xs,
  },
  dateText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.gray500,
    marginLeft: 4,
  },
  completedIcon: {
    marginLeft: theme.spacing.md,
  },
  completedText: {
    fontSize: theme.fontSizes.xs,
    color: theme.colors.success,
    marginLeft: 4,
  },
  moreItemsText: {
    textAlign: "center",
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray500,
    fontStyle: "italic",
    marginTop: theme.spacing.sm,
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
