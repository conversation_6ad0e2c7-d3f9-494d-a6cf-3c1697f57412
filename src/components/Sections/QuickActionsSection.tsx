import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { useNavigation } from "@react-navigation/native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Section } from "../main";
import { Row } from "../main/Row";
import { theme } from "../../theme";
import { DASHBOARD_SCREENS } from "../../navigation/constants";

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: keyof typeof MaterialCommunityIcons.glyphMap;
  color: string;
  screen: string;
}

const quickActions: QuickAction[] = [
  {
    id: "complaint",
    title: "Nueva Queja",
    description: "Reportar un problema o inconformidad",
    icon: "flag-outline",
    color: theme.colors.error,
    screen: DASHBOARD_SCREENS.CREATE_COMPLAINT,
  },
  {
    id: "maintenance",
    title: "Reporte de Mantenimiento",
    description: "Reportar problema de mantenimiento",
    icon: "tools",
    color: theme.colors.warning,
    screen: DASHBOARD_SCREENS.CREATE_MAINTENANCE_REPORT,
  },
];

export const QuickActionsSection: React.FC = () => {
  const navigation = useNavigation();

  const handleActionPress = (screen: string) => {
    navigation.navigate(screen as never);
  };

  return (
    <Section title="Acciones Rápidas">
      <Row gap={theme.spacing.md} style={styles.container}>
        {quickActions.map((action) => (
          <TouchableOpacity
            key={action.id}
            onPress={() => handleActionPress(action.screen)}
            activeOpacity={0.7}
          >
            <View
              style={[
                styles.iconContainer,
                { backgroundColor: `${action.color}20` },
              ]}
            >
              <MaterialCommunityIcons
                name={action.icon}
                size={theme.fontSizes.xl}
                color={action.color}
              />
            </View>
          </TouchableOpacity>
        ))}
      </Row>
    </Section>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: theme.spacing.md,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: theme.radii.lg,
    alignItems: "center",
    justifyContent: "center",
  },
  textContainer: {
    flex: 1,
    marginLeft: theme.spacing.md,
  },
  actionTitle: {
    fontSize: theme.fontSizes.md,
    fontWeight: "700",
    color: theme.colors.gray900,
    marginBottom: theme.spacing.xs,
  },
  actionDescription: {
    fontSize: theme.fontSizes.sm,
    color: theme.colors.gray700,
    lineHeight: 18,
  },
});
