import { Card, Row, Section } from "../main";
import {
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  ImageBackground,
  View,
  Dimensions,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import { Col } from "../main/Col";
import { theme } from "../../theme";
import { PartialAnnouncement } from "../../interfaces/me";
import { getDateFromJSDate } from "../../utils/date-time.utils";

interface AnnouncementSection {
  announcements: PartialAnnouncement[];
  onAnnouncementPress: (announcement: PartialAnnouncement) => void;
}

const { width: screenWidth } = Dimensions.get("window");

export const AnnouncementSection: React.FC<AnnouncementSection> = ({
  announcements,
  onAnnouncementPress,
}) => {
  if (!announcements.length) {
    return (
      <Section title="Comunicados">
        <Text style={styles.noDataText}>No hay comunicados disponibles</Text>
      </Section>
    );
  }

  return (
    <Section title="Comunicados">
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        scrollEventThrottle={16}
        contentContainerStyle={styles.scrollContainer}
      >
        {announcements.map((announcement, index) => (
          <TouchableOpacity
            key={announcement.id}
            onPress={() => onAnnouncementPress(announcement)}
            activeOpacity={0.9}
            style={[
              styles.cardContainer,
              index === 0 && styles.firstCard,
              index === announcements.length - 1 && styles.lastCard,
            ]}
          >
            <Card style={styles.card}>
              <ImageBackground
                source={{
                  uri:
                    announcement.images?.[0]?.path ||
                    announcement.imageUrl ||
                    "",
                }}
                style={styles.imageBackground}
                imageStyle={styles.imageStyle}
              >
                {/* Overlay gradient */}
                <View style={styles.overlay} />

                {/* Content */}
                <Col style={styles.mainContainer}>
                  {/* Role badge */}
                  {announcement.role && (
                    <View style={styles.roleBadge}>
                      <MaterialCommunityIcons
                        name="account-tie"
                        size={12}
                        color={theme.colors.white}
                      />
                      <Text style={styles.roleText}>
                        {announcement.role.name}
                      </Text>
                    </View>
                  )}

                  {/* Title */}
                  <Text style={styles.title} numberOfLines={3}>
                    {announcement.title}
                  </Text>

                  {/* Message preview */}
                  <Text style={styles.messagePreview} numberOfLines={2}>
                    {announcement.message}
                  </Text>

                  {/* Footer */}
                  <Row
                    justify="space-between"
                    align="center"
                    style={styles.footer}
                  >
                    <View style={styles.dateContainer}>
                      <MaterialCommunityIcons
                        name="calendar"
                        size={14}
                        color={theme.colors.gold}
                      />
                      <Text style={styles.date}>
                        {getDateFromJSDate(announcement.createdAt)}
                      </Text>
                    </View>
                    <View style={styles.readMoreContainer}>
                      <Text style={styles.readMoreText}>Leer más</Text>
                      <MaterialCommunityIcons
                        name="chevron-right"
                        size={16}
                        color={theme.colors.white}
                      />
                    </View>
                  </Row>
                </Col>
              </ImageBackground>
            </Card>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </Section>
  );
};

const styles = StyleSheet.create({
  scrollContainer: {
    paddingHorizontal: theme.spacing.sm,
  },
  cardContainer: {
    marginRight: theme.spacing.md,
  },
  firstCard: {
    marginLeft: 0,
  },
  lastCard: {
    marginRight: theme.spacing.lg,
  },
  card: {
    width: screenWidth * 0.8,
    height: 280,
    padding: 0,
    overflow: "hidden",
  },
  imageBackground: {
    flex: 1,
    justifyContent: "flex-end",
  },
  imageStyle: {
    borderRadius: theme.radii.lg,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(0, 0, 0, 0.4)",
    borderRadius: theme.radii.lg,
  },
  mainContainer: {
    padding: theme.spacing.lg,
    flex: 1,
    justifyContent: "space-between",
    zIndex: 1,
  },
  roleBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 4,
    borderRadius: theme.radii.sm,
    alignSelf: "flex-start",
    marginBottom: theme.spacing.sm,
  },
  roleText: {
    color: theme.colors.white,
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    marginLeft: 4,
    textTransform: "uppercase",
  },
  title: {
    color: theme.colors.white,
    fontWeight: "700",
    fontSize: theme.fontSizes.lg,
    lineHeight: 24,
    textShadowColor: "rgba(0, 0, 0, 0.5)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
    marginBottom: theme.spacing.sm,
  },
  messagePreview: {
    color: theme.colors.gray200,
    fontSize: theme.fontSizes.sm,
    lineHeight: 18,
    marginBottom: theme.spacing.md,
    textShadowColor: "rgba(0, 0, 0, 0.5)",
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  footer: {
    marginTop: "auto",
  },
  dateContainer: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.3)",
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 4,
    borderRadius: theme.radii.sm,
  },
  date: {
    color: theme.colors.gold,
    fontSize: theme.fontSizes.xs,
    fontWeight: "600",
    marginLeft: 4,
  },
  readMoreContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  readMoreText: {
    color: theme.colors.white,
    fontSize: theme.fontSizes.sm,
    fontWeight: "600",
    marginRight: 4,
  },
  noDataText: {
    textAlign: "center",
    fontSize: theme.fontSizes.md,
    color: theme.colors.gray500,
    padding: theme.spacing.lg,
    fontStyle: "italic",
  },
});
