import React from "react";
import { Text, StyleSheet } from "react-native";
import { theme } from "../../../theme";

interface MessageProps {
  text: string;
}

export const Message: React.FC<MessageProps> = ({ text }) => {
  return <Text style={styles.text}>{text}</Text>;
};

const styles = StyleSheet.create({
  text: {
    backgroundColor: theme.colors.gray200,
    borderRadius: theme.radii.xl,
    color: theme.colors.gray700,
    fontSize: theme.fontSizes.sm,
    textAlign: "center",
    padding: theme.spacing.md,
  },
});
